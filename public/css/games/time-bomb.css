/**
 * Person Picker - A random name picker with fun games
 * Copyright (C) 2024 PersonPicker.com
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 * 
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

/* Enhanced styles for Time Bomb game */
#game-container {
    max-width: 800px;
    margin: 40px auto;
    text-align: center;
    padding: 30px;
    border: 2px solid #ff4444;
    border-radius: 15px;
    background: linear-gradient(145deg, #2c2c2c, #1a1a1a);
    box-shadow: 0 8px 32px rgba(255, 68, 68, 0.2);
    color: #fff;
}

h1 {
    color: #ff4444;
    text-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
    font-size: 2.5em;
    margin-bottom: 30px;
    animation: pulse 2s infinite;
}

#countdown {
    font-size: 36px;
    margin: 20px;
    font-family: 'Courier New', monospace;
    color: #ff4444;
    text-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
    font-weight: bold;
}

#current-holder {
    font-size: 32px;
    margin: 30px;
    font-weight: bold;
    display: none;
    padding: 20px;
    background: rgba(255, 68, 68, 0.1);
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(255, 68, 68, 0.2);
}

#status {
    font-size: 24px;
    margin: 20px;
    color: #ff4444;
    text-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
}

button {
    font-size: 18px;
    padding: 15px 30px;
    margin: 10px;
    cursor: pointer;
    background: linear-gradient(145deg, #ff4444, #ff0000);
    border: none;
    color: white;
    border-radius: 25px;
    text-transform: uppercase;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 68, 68, 0.4);
}

button:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
}

/* Enhanced bomb explosion animation */
@keyframes bombExplodeAnim {
    0% { transform: scale(1) rotate(0deg); opacity: 1; filter: blur(0); }
    50% { transform: scale(2) rotate(180deg); opacity: 1; filter: blur(2px); }
    75% { transform: scale(2.5) rotate(270deg); opacity: 0.5; filter: blur(4px); }
    100% { transform: scale(3) rotate(360deg); opacity: 0; filter: blur(8px); }
}

/* Pulsing animation for various elements */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Enhanced bomb passing animation */
@keyframes passBomb {
    0% { transform: scale(1) translateX(-20px) rotate(-10deg); }
    50% { transform: scale(1.3) translateX(10px) rotate(5deg); }
    100% { transform: scale(1) translateX(0) rotate(0deg); }
}

/* Ticking animation for the bomb icon */
@keyframes tick {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

#bomb-animation {
    font-size: 80px;
    display: none;
    margin: 20px;
    filter: drop-shadow(0 0 10px rgba(255, 68, 68, 0.5));
}

#bomb-animation.explode {
    animation: bombExplodeAnim 1.5s ease-out forwards;
}

.bomb-icon {
    animation: tick 0.5s infinite;
    filter: drop-shadow(0 0 5px rgba(255, 68, 68, 0.5));
}

.bomb-icon.passed {
    animation: passBomb 0.4s ease-out;
}

/* Warning flash animation for low time */
@keyframes warningFlash {
    0% { background: linear-gradient(145deg, #2c2c2c, #1a1a1a); }
    50% { background: linear-gradient(145deg, #3c1f1f, #2c1515); }
    100% { background: linear-gradient(145deg, #2c2c2c, #1a1a1a); }
}

.warning {
    animation: warningFlash 0.5s infinite;
} 