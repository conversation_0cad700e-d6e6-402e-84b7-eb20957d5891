/**
 * Person Picker - A random name picker with fun games
 * Copyright (C) 2024 PersonPicker.com
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 * 
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --danger-color: #0D0630;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --input-background: #FFFFFF;
    --input-border: #8BBEB2;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
}

.container {
    background-color: var(--card-background);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 90%;
    text-align: center;
}

h1 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

#description {
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

#gameArea {
    width: 100%;
    height: 400px;
    background-color: var(--background-color);
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    margin-top: 20px;
}

.balloon {
    position: absolute;
    text-align: center;
    transition: top 0.1s linear;
}

.balloon-svg {
    width: 60px;
    height: 80px;
}

.name-label {
    font-size: 12px;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px black;
}

.dart {
    position: absolute;
    width: 20px;
    height: 20px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 22L16 18H13V2H11V18H8L12 22Z" fill="%23333"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

@keyframes pop {
    0% { transform: scale(1); opacity: 1; }
    20% { transform: scale(1.2); opacity: 0.8; }
    40% { transform: scale(0.8); opacity: 0.6; }
    60% { transform: scale(1.1); opacity: 0.4; }
    80% { transform: scale(0.9); opacity: 0.2; }
    100% { transform: scale(0); opacity: 0; }
}

.popping {
    animation: pop 0.5s ease-out forwards;
}

#startButton {
    font-size: 18px;
    padding: 10px 20px;
    cursor: pointer;
    margin: 10px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    transition: background-color 0.3s ease, transform 0.1s ease;
}

#startButton:hover {
    opacity: 0.9;
}

#startButton:active {
    transform: scale(0.98);
}

#startButton:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

@media (max-width: 600px) {
    .container {
        padding: 1rem;
    }
    #gameArea {
        height: 300px;
    }
} 