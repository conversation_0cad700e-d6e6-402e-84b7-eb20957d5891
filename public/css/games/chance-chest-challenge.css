/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --danger-color: #0D0630;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --input-background: #FFFFFF;
    --input-border: #8BBEB2;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
}

.container {
    background-color: var(--card-background);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

#description {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    max-width: 600px;
}

.briefcases {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin: 20px 0;
}

.briefcase {
    width: 80px;
    height: 60px;
    background-color: var(--accent-color);
    border: 2px solid var(--secondary-color);
    border-radius: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--card-background);
    font-size: 14px;
    text-align: center;
    padding: 5px;
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.briefcase:hover {
    transform: scale(1.05);
}

.briefcase.selected {
    background-color: var(--primary-color);
}

#message {
    font-size: 18px;
    margin: 20px 0;
    min-height: 50px;
    text-align: center;
}

button {
    font-size: 18px;
    padding: 10px 20px;
    cursor: pointer;
    margin: 10px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    transition: background-color 0.3s ease, transform 0.1s ease;
}

button:hover {
    opacity: 0.9;
}

button:active {
    transform: scale(0.98);
}

#teamList {
    margin: 20px 0;
    text-align: center;
}

.team-member {
    display: inline-block;
    margin: 5px;
    padding: 5px 10px;
    background-color: var(--input-background);
    color: var(--text-color);
    border-radius: 5px;
}

.team-member.current {
    background-color: var(--primary-color);
    color: white;
}

@keyframes confetti {
    0% { transform: translateY(0) rotateZ(0deg); opacity: 1; }
    100% { transform: translateY(1000px) rotateZ(720deg); opacity: 0; }
}

.confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    background-color: #f0f;
    z-index: 1000;
    animation: confetti 3s ease-out forwards;
}

@keyframes spin {
    0% { transform: rotateY(0deg); }
    100% { transform: rotateY(360deg); }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7); }
    70% { box-shadow: 0 0 0 20px rgba(102, 126, 234, 0); }
    100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0); }
}

.winner {
    animation: spin 1s ease-in-out, pulse 1s infinite;
}

#winnerMessage {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-top: 20px;
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.5s ease-in-out;
}

#winnerMessage.show {
    opacity: 1;
    transform: scale(1);
}

@media (max-width: 600px) {
    .container {
        padding: 1rem;
    }
    .briefcase {
        font-size: 12px;
    }
} 