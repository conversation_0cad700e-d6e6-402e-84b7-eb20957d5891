/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap');

:root {
    --primary-color: #18314F;
    --secondary-color: #384E77;
    --accent-color: #8BBEB2;
    --danger-color: #0D0630;
    --text-color: #0D0630;
    --background-color: #FFFFFF;
    --card-background: #FFFFFF;
    --input-background: #FFFFFF;
    --input-border: #8BBEB2;
    --track-color: #90EE90;
    --track-border: #228B22;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    margin: 0;
    color: var(--text-color);
}

.container {
    background-color: var(--card-background);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 90%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

#description {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    max-width: 600px;
}

#raceTrack {
    position: relative;
    width: 100%;
    height: 400px;
    background-color: var(--track-color);
    border: 2px solid var(--track-border);
    overflow: hidden;
    border-radius: 10px;
}

.dinosaur {
    position: absolute;
    width: 60px;
    height: 60px;
    background-size: contain;
    background-repeat: no-repeat;
    transition: left 0.1s linear;
}

.dinoName {
    position: absolute;
    left: 65px;
    width: 100px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
}

#finishLine {
    position: absolute;
    right: 0;
    top: 0;
    width: 20px;
    height: 100%;
    background-image: 
        linear-gradient(45deg, #000 25%, transparent 25%),
        linear-gradient(-45deg, #000 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #000 75%),
        linear-gradient(-45deg, transparent 75%, #000 75%);
    background-size: 10px 10px;
    background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
    border-left: 2px solid #000;
}

#message {
    font-size: 18px;
    margin: 20px 0;
    min-height: 50px;
    text-align: center;
}

button {
    font-size: 18px;
    padding: 10px 20px;
    cursor: pointer;
    margin: 10px;
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 5px;
    transition: background-color 0.3s ease, transform 0.1s ease;
}

button:hover {
    opacity: 0.9;
}

button:active {
    transform: scale(0.98);
}

@keyframes confetti {
    0% { transform: translateY(0) rotateZ(0deg); opacity: 1; }
    100% { transform: translateY(1000px) rotateZ(720deg); opacity: 0; }
}

.confetti {
    position: fixed;
    width: 10px;
    height: 10px;
    background-color: #f0f;
    z-index: 1000;
    animation: confetti 3s ease-out forwards;
}

#winnerMessage {
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    margin-top: 20px;
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.5s ease-in-out;
}

#winnerMessage.show {
    opacity: 1;
    transform: scale(1);
}

@media (max-width: 600px) {
    .container {
        padding: 1rem;
    }
    #raceTrack {
        height: 200px;
    }
    .dinosaur {
        width: 30px;
        height: 30px;
    }
    .dinoName {
        font-size: 10px;
        left: 35px;
    }
} 