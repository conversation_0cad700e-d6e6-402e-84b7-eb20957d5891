/**
 * Person Picker - Hot Potato Race Game Styles
 * Copyright (C) 2024 PersonPicker.com
 */

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes wobble {
    0% { transform: translateX(0); }
    15% { transform: translateX(-10px) rotate(-5deg); }
    30% { transform: translateX(8px) rotate(4deg); }
    45% { transform: translateX(-6px) rotate(-2deg); }
    60% { transform: translateX(4px) rotate(1deg); }
    75% { transform: translateX(-2px) rotate(-1deg); }
    100% { transform: translateX(0); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

@keyframes passing {
    0% { transform: translateX(-100px) translateY(0) scale(0.8); opacity: 0.7; }
    50% { transform: translateX(0) translateY(-20px) scale(1.2); opacity: 1; }
    100% { transform: translateX(100px) translateY(0) scale(0.8); opacity: 0.7; }
}

@keyframes celebrate {
    0% { transform: scale(1); }
    50% { transform: scale(1.3) rotate(10deg); }
    100% { transform: scale(1); }
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    color: #333;
}

#game-container {
    max-width: 800px;
    margin: 20px auto;
    padding: 20px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
}

h1 {
    color: #ff6b6b;
    margin-bottom: 15px;
    font-size: 32px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

#description {
    margin-bottom: 25px;
    color: #666;
    font-size: 16px;
    line-height: 1.5;
}

#round-info {
    font-size: 24px;
    font-weight: bold;
    color: #4a6ee0;
    margin: 15px 0;
}

#timer {
    font-size: 18px;
    font-weight: bold;
    color: #ff6b6b;
    margin-bottom: 20px;
    padding: 8px;
    border-radius: 8px;
    background-color: #fff6f6;
    display: inline-block;
    min-width: 200px;
}

#timer.warning {
    animation: pulse 0.5s infinite;
    background-color: #ffe2e2;
    color: #ff0000;
}

#players-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
}

.player {
    padding: 10px 15px;
    background-color: #ebf3ff;
    border-radius: 8px;
    font-weight: bold;
    min-width: 120px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.player.active {
    background-color: #ffefd5;
    box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
    transform: scale(1.05);
}

.player.eliminated {
    opacity: 0.5;
    text-decoration: line-through;
    transform: scale(0.95);
}

#potato-container {
    margin: 30px auto;
    height: 100px;
    position: relative;
}

#potato {
    font-size: 48px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    transition: all 0.3s ease;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    z-index: 10;
}

#potato.hot {
    animation: wobble 0.8s infinite;
    filter: drop-shadow(0 4px 12px rgba(255, 100, 0, 0.4));
}

#potato.passing {
    animation: passing 0.5s ease;
}

#eliminated-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f7f7f7;
    border-radius: 8px;
    max-height: 150px;
    overflow-y: auto;
}

#eliminated-container h3 {
    margin-top: 0;
    color: #555;
}

#eliminated-list {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

#eliminated-list li {
    padding: 5px;
    border-bottom: 1px solid #eee;
}

#winner-display {
    font-size: 28px;
    font-weight: bold;
    color: #ffa502;
    margin: 20px 0;
    min-height: 40px;
}

#winner-display.show {
    animation: celebrate 1s ease;
}

#controls {
    margin-top: 25px;
}

button {
    background-color: #4a6ee0;
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
    margin: 0 5px;
    transition: background-color 0.3s, transform 0.2s;
}

button:hover {
    background-color: #3a5ecc;
    transform: translateY(-2px);
}

button:active {
    transform: translateY(0);
}

#reset-btn {
    background-color: #ff6b6b;
}

#reset-btn:hover {
    background-color: #ff5252;
}

.warning {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* Responsive adjustments */
@media (max-width: 600px) {
    #game-container {
        margin: 10px;
        padding: 15px;
    }

    h1 {
        font-size: 24px;
    }

    #description {
        font-size: 14px;
    }

    #timer {
        font-size: 16px;
        min-width: 150px;
    }

    .player {
        min-width: 100px;
        font-size: 14px;
    }

    #potato {
        font-size: 36px;
    }

    #winner-display {
        font-size: 22px;
    }

    button {
        padding: 10px 20px;
        font-size: 14px;
    }
}