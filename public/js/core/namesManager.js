/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

class NamesManager {
    constructor() {
        this.elements = {
            nameInputs: document.getElementById('nameInputs'),
            addNameBtn: document.getElementById('addNameBtn')
        };
        this.names = JSON.parse(localStorage.getItem('names') || '[]');
        this.initializeEventListeners();
    }

    initializeNameInputs() {
        this.names.forEach(name => this.addNameInput(name));
        if (this.names.length === 0) {
            this.addNameInput();
            this.addNameInput();
        }
    }

    addNameInput(value = '') {
        const inputContainer = document.createElement('div');
        inputContainer.className = 'input-container';

        const input = document.createElement('input');
        input.type = 'text';
        input.value = value;
        input.placeholder = 'Enter a name';
        input.setAttribute('aria-label', 'Enter a name');
        input.addEventListener('input', () => this.updateNames());

        const removeBtn = document.createElement('button');
        removeBtn.className = 'removeBtn';
        removeBtn.textContent = '×';
        removeBtn.setAttribute('aria-label', 'Remove name');
        removeBtn.addEventListener('click', () => {
            inputContainer.remove();
            this.updateNames();
        });

        inputContainer.appendChild(input);
        inputContainer.appendChild(removeBtn);
        this.elements.nameInputs.appendChild(inputContainer);
    }

    updateNames() {
        const inputs = this.elements.nameInputs.querySelectorAll('input');
        this.names = Array.from(inputs).map(input => input.value.trim()).filter(Boolean);
        localStorage.setItem('names', JSON.stringify(this.names));
        
        // Dispatch event when names are updated
        const event = new CustomEvent('namesUpdated', {
            detail: { names: this.names }
        });
        document.dispatchEvent(event);
    }

    getNames() {
        return this.names;
    }

    initializeEventListeners() {
        this.elements.addNameBtn.addEventListener('click', () => this.addNameInput());
    }
}

export default NamesManager; 