console.log("Confetti.js loaded");

function createConfetti(container, count = 100, duration = 3000) {
    const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff'];
    const shapes = ['square', 'rectangle', 'circle'];
    
    if (getComputedStyle(container).position === 'static') {
        container.style.position = 'relative';
    }
    
    const optimizedCount = Math.min(count, 50);
    
    const batchSize = 10;
    let currentBatch = 0;
    
    function createBatch() {
        if (currentBatch >= optimizedCount) return;
        
        const start = currentBatch;
        const end = Math.min(start + batchSize, optimizedCount);
        
        for (let i = start; i < end; i++) {
            const confetti = document.createElement('div');
            confetti.style.position = 'absolute';
            confetti.style.left = Math.random() * 100 + '%';
            confetti.style.top = '-5%';
            
            const shape = shapes[Math.floor(Math.random() * shapes.length)];
            if (shape === 'square') {
                confetti.style.width = '8px';
                confetti.style.height = '8px';
            } else if (shape === 'rectangle') {
                confetti.style.width = '12px';
                confetti.style.height = '6px';
            } else {
                confetti.style.width = '8px';
                confetti.style.height = '8px';
                confetti.style.borderRadius = '50%';
            }
            
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.transform = 'translate3d(0,0,0)';
            container.appendChild(confetti);

            const randomX = Math.random() * 300 - 150;
            const randomRotation = Math.random() * 720 - 360;

            confetti.animate([
                { transform: 'translate3d(0, 0, 0) rotate(0deg)', opacity: 1 },
                { transform: `translate3d(${randomX}px, ${container.offsetHeight}px, 0) rotate(${randomRotation}deg)`, opacity: 0 }
            ], {
                duration: Math.random() * duration + 1000,
                easing: 'cubic-bezier(0.25, 1, 0.5, 1)'
            }).onfinish = () => {
                confetti.remove();
            };
        }
        
        currentBatch = end;
        if (currentBatch < optimizedCount) {
            requestAnimationFrame(createBatch);
        }
    }
    
    requestAnimationFrame(createBatch);
}