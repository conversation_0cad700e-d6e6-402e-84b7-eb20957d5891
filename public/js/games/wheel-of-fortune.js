/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

let data = [];
let wheelGroup;
let rotation = 0;
let spinning = false;

function createWheel() {
    const chartContainer = document.getElementById('wheel-container');
    const width = chartContainer.offsetWidth;
    const height = chartContainer.offsetHeight;
    const radius = Math.min(width, height) / 2;

    const color = d3.scaleOrdinal(d3.schemeCategory10);

    d3.select("#chart").selectAll("*").remove();

    const svg = d3.select("#chart")
        .append("svg")
        .attr("width", "100%")
        .attr("height", "100%")
        .attr("viewBox", `0 0 ${width} ${height}`)
        .append("g")
        .attr("transform", `translate(${width / 2},${height / 2})`);

    const arc = d3.arc()
        .innerRadius(radius * 0.2)
        .outerRadius(radius * 0.9);

    const pie = d3.pie()
        .sort(null)
        .value(d => d.value);

    wheelGroup = svg.append("g");

    data = JSON.parse(localStorage.getItem('names') || '[]').map(name => ({label: name, value: 1}));
    
    if (data.length < 2) {
        alert("Please add at least two names in the launcher before starting the game.");
        return;
    }

    const arcs = wheelGroup.selectAll(".slice")
        .data(pie(data))
        .enter().append("g")
        .attr("class", "slice");

    arcs.append("path")
        .attr("d", arc)
        .style("fill", (d, i) => color(i))
        .style("stroke", "#fff")
        .style("stroke-width", "2px");

    arcs.append("text")
        .attr("transform", d => {
            const [x, y] = arc.centroid(d);
            return `translate(${x * 1.2}, ${y * 1.2})`;
        })
        .attr("dy", ".35em")
        .text(d => d.data.label)
        .style("text-anchor", "middle")
        .style("font-size", "14px");

    // Add a central circle
    svg.append("circle")
        .attr("cx", 0)
        .attr("cy", 0)
        .attr("r", radius * 0.15)
        .attr("fill", "#fff")
        .attr("stroke", "#000")
        .attr("stroke-width", 2);

    // Update the arrow position
    d3.select("#arrow")
        .style("top", "0")
        .style("left", "50%")
        .style("transform", "translateX(-50%)")
        .style("border-left", "20px solid transparent")
        .style("border-right", "20px solid transparent")
        .style("border-top", "40px solid var(--text-color)")
        .style("border-bottom", "none");

    // Append the spin button to the chart container
    const spinButton = d3.select("#wheel-container")
        .append("button")
        .attr("id", "spin-button")
        .text("SPIN")
        .on("click", spin);
}

function spin() {
    if (spinning) return;
    spinning = true;
    const oldRotation = rotation % 360;
    const spinAngle = Math.random() * 360 + 720 + Math.random() * 720; // Random angle between 2 to 4 full rotations
    rotation += spinAngle;
    
    wheelGroup.transition()
        .duration(4000)
        .ease(d3.easeQuadInOut)
        .attrTween("transform", () => {
            return t => `rotate(${oldRotation + t * spinAngle})`;
        })
        .on("end", () => {
            spinning = false;
            const winnerIndex = Math.floor(((360 - (rotation % 360)) % 360) / (360 / data.length));
            const winner = data[winnerIndex].label;
            document.getElementById('question').textContent = `The winner is: ${winner}!`;
            document.getElementById('question').classList.add('celebrate');
            window.parent.confetti?.({
                particleCount: 100,
                spread: 70,
                origin: { y: 0.6 }
            });
        });
}

window.addEventListener('load', createWheel);
window.addEventListener('resize', createWheel); 