/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

let teamMembers = window.parent.names || [];
let selectionOrder = [];
let selectedCases = {};
let currentPlayerIndex = 0;

function startGame() {
    teamMembers = window.parent.names || [];
    if (teamMembers.length < 2) {
        alert('Please add at least two names in the launcher before starting the game.');
        return;
    }

    selectedCases = {};
    currentPlayerIndex = 0;
    selectionOrder = [...teamMembers];
    shuffleArray(selectionOrder);
    createBriefcases();
    updateTeamList();
    updateMessage(`${selectionOrder[currentPlayerIndex]}, select a chance chest!`);
    document.getElementById('revealButton').disabled = true;
    document.getElementById('winnerMessage').textContent = '';
    document.getElementById('winnerMessage').classList.remove('show');
}

function createBriefcases() {
    const briefcasesContainer = document.getElementById('briefcases');
    briefcasesContainer.innerHTML = '';
    for (let i = 0; i < teamMembers.length; i++) {
        const briefcase = document.createElement('div');
        briefcase.className = 'briefcase';
        briefcase.textContent = i + 1;
        briefcase.onclick = () => selectBriefcase(i);
        briefcasesContainer.appendChild(briefcase);
    }
}

function selectBriefcase(index) {
    if (selectedCases[index] || Object.keys(selectedCases).length >= teamMembers.length) {
        return;
    }

    const briefcase = document.getElementsByClassName('briefcase')[index];
    briefcase.classList.add('selected');
    selectedCases[index] = selectionOrder[currentPlayerIndex];

    if (Object.keys(selectedCases).length === teamMembers.length) {
        updateMessage("All chance chests selected! You can now reveal the winner.");
        document.getElementById('revealButton').disabled = false;
    } else {
        currentPlayerIndex = (currentPlayerIndex + 1) % teamMembers.length;
        updateMessage(`${selectionOrder[currentPlayerIndex]}, select a chance chest!`);
        updateTeamList();
    }
}

function revealWinner() {
    const winningIndex = Object.keys(selectedCases)[Math.floor(Math.random() * teamMembers.length)];
    const winner = selectedCases[winningIndex];
    
    const briefcases = document.getElementsByClassName('briefcase');
    for (let i = 0; i < briefcases.length; i++) {
        briefcases[i].textContent = truncateName(selectedCases[i]);
        briefcases[i].style.backgroundColor = 'var(--input-background)';
        briefcases[i].style.color = 'var(--text-color)';
    }

    setTimeout(() => {
        briefcases[winningIndex].classList.add('winner');
        briefcases[winningIndex].style.backgroundColor = 'var(--primary-color)';
        briefcases[winningIndex].style.color = 'white';
        
        window.parent.confetti?.({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 }
        });
        
        const winnerMessage = document.getElementById('winnerMessage');
        winnerMessage.textContent = `🎉 ${winner} has uncovered the winning case! 🎉`;
        winnerMessage.classList.add('show');
        
        updateMessage("Congratulations to our winner!");
    }, 1000);

    document.getElementById('revealButton').disabled = true;
}

function truncateName(name) {
    return name.length > 8 ? name.substring(0, 7) + '.' : name;
}

function updateTeamList() {
    const teamList = document.getElementById('teamList');
    teamList.innerHTML = selectionOrder.map((member, index) => 
        `<span class="team-member${index === currentPlayerIndex ? ' current' : ''}">${member}</span>`
    ).join('');
}

function updateMessage(msg) {
    document.getElementById('message').textContent = msg;
}

function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
}

// Initialize
startGame(); 