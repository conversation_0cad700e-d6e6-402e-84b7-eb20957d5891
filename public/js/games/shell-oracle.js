/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

let names = window.parent.names || [];
let isOpen = false;

function openShell() {
    if (isOpen) return;
    
    const shellTop = document.querySelector('.shell-top');
    const pearl = document.querySelector('.pearl');
    const message = document.getElementById('message');
    const shellContainer = document.getElementById('shellContainer');

    shellTop.style.transform = 'rotate(-30deg) translateY(-20px)';
    
    setTimeout(() => {
        pearl.style.transform = 'translate(-50%, -50%) scale(1)';
        pearl.style.animation = 'glowing 1s ease-in-out infinite alternate';
        shellContainer.style.cursor = 'default';

        if (names.length > 0) {
            let i = 0;
            const interval = setInterval(() => {
                message.textContent = names[Math.floor(Math.random() * names.length)];
                message.style.opacity = '1';
                i++;
                if (i >= 20) {
                    clearInterval(interval);
                    const chosenName = names[Math.floor(Math.random() * names.length)];
                    message.textContent = `The oracle has chosen: ${chosenName}`;
                    window.parent.confetti?.({
                        particleCount: 100,
                        spread: 70,
                        origin: { y: 0.6 }
                    });
                }
            }, 100);
        } else {
            message.textContent = "No names available. The oracle remains silent.";
            message.style.opacity = '1';
        }
    }, 750);

    isOpen = true;
}

// Initialize
document.getElementById('shellContainer').addEventListener('click', openShell); 