/**
 * Person Picker - A random name picker with fun games
 * Copyright (C) 2024 PersonPicker.com
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 * 
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 */

const gameArea = document.getElementById('gameArea');
const messageEl = document.getElementById('message');
const startButton = document.getElementById('startButton');
let names = [];
let balloons = [];
let darts = [];
let gameRunning = false;
let animationFrame;
let collisionDetectionEnabled = false;

class Balloon {
    constructor(name, x) {
        this.name = name;
        this.element = document.createElement('div');
        this.element.className = 'balloon';
        this.element.style.left = `${x}px`;
        
        const balloonSvg = `<svg class="balloon-svg" viewBox="0 0 50 60">
            <path d="M25 0 C10 0 0 15 0 30 C0 45 10 60 25 60 C40 60 50 45 50 30 C50 15 40 0 25 0 Z" fill="${this.getRandomColor()}"/>
            <path d="M25 60 L20 70 L30 70 Z" fill="#8B4513"/>
        </svg>`;
        
        this.element.innerHTML = `${balloonSvg}<div class="name-label">${name}</div>`;
        gameArea.appendChild(this.element);
        
        this.speed = Math.random() * 0.15 + 0.1;
        this.y = gameArea.clientHeight - 100; // Start inside the game area
        this.width = 60;
        this.height = 80;
        this.element.style.top = `${this.y}px`;
        this.initialY = this.y;
    }

    getRandomColor() {
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    move() {
        this.y -= this.speed;
        this.element.style.top = `${this.y}px`;
        return this.y + this.height < 0;
    }

    pop() {
        this.element.classList.add('popping');
        setTimeout(() => {
            this.element.remove();
        }, 500);
    }

    getRect() {
        const rect = this.element.getBoundingClientRect();
        const gameAreaRect = gameArea.getBoundingClientRect();
        return {
            left: rect.left - gameAreaRect.left,
            top: rect.top - gameAreaRect.top,
            right: rect.right - gameAreaRect.left,
            bottom: rect.bottom - gameAreaRect.top
        };
    }
}

class Dart {
    constructor() {
        this.element = document.createElement('div');
        this.element.className = 'dart';
        this.x = Math.random() * (gameArea.clientWidth - 20);
        this.y = -20;
        this.speed = Math.random() * 1 + 0.5;
        this.element.style.left = `${this.x}px`;
        this.element.style.top = `${this.y}px`;
        gameArea.appendChild(this.element);
    }

    move() {
        this.y += this.speed;
        this.element.style.top = `${this.y}px`;
        return this.y > gameArea.clientHeight;
    }

    remove() {
        this.element.remove();
    }

    getRect() {
        return {
            left: this.x,
            top: this.y,
            right: this.x + 20,
            bottom: this.y + 20
        };
    }
}

function createBalloons() {
    gameArea.innerHTML = '';
    const spacing = gameArea.clientWidth / (names.length + 1);
    balloons = names.map((name, index) => new Balloon(name, spacing * (index + 1)));
}

function animateParade() {
    balloons.forEach((balloon, index) => {
        if (balloon.move()) {
            balloon.y = gameArea.clientHeight - 100;
        }
    });

    darts.forEach((dart, index) => {
        if (dart.move()) {
            dart.remove();
            darts.splice(index, 1);
        }
    });

    if (collisionDetectionEnabled) {
        checkCollisions();
    }

    if (Math.random() < 0.03) {  // Increased from 0.015 to 0.03
        darts.push(new Dart());
    }

    if (balloons.length > 1) {
        animationFrame = requestAnimationFrame(animateParade);
    } else {
        endGame();
    }
}

function checkCollisions() {
    for (let i = balloons.length - 1; i >= 0; i--) {
        const balloon = balloons[i];
        if (balloon.y < balloon.initialY) {  // Only check collisions if balloon has moved
            const balloonRect = balloon.getRect();
            for (let j = darts.length - 1; j >= 0; j--) {
                const dartRect = darts[j].getRect();
                if (
                    balloonRect.left < dartRect.right &&
                    balloonRect.right > dartRect.left &&
                    balloonRect.top < dartRect.bottom &&
                    balloonRect.bottom > dartRect.top
                ) {
                    balloon.pop();
                    darts[j].remove();
                    darts.splice(j, 1);
                    messageEl.textContent = `${balloon.name}'s balloon popped!`;
                    balloons.splice(i, 1);
                    break;
                }
            }
        }
    }
}

function resetGame() {
    cancelAnimationFrame(animationFrame);
    gameArea.innerHTML = '';
    balloons = [];
    darts = [];
    gameRunning = false;
    collisionDetectionEnabled = false;
}

function startGame() {
    resetGame();
    gameRunning = true;
    startButton.disabled = true;
    messageEl.textContent = 'The parade has started!';
    createBalloons();
    animateParade();
    
    // Enable collision detection after a short delay
    setTimeout(() => {
        collisionDetectionEnabled = true;
    }, 1000);
}

function endGame() {
    gameRunning = false;
    startButton.disabled = false;
    const winner = balloons[0];
    messageEl.textContent = `${winner.name} wins!`;
    cancelAnimationFrame(animationFrame);
    
    celebrateWinner();
}

function celebrateWinner() {
    if (typeof createConfetti === 'function') {
        createConfetti(gameArea);
    }
}

startButton.addEventListener('click', startGame);

// Initialize the game when the page loads
window.addEventListener('load', () => {
    names = JSON.parse(localStorage.getItem('names') || '[]');
    if (names.length < 2) {
        messageEl.textContent = 'Please add at least two names before starting the parade.';
        startButton.disabled = true;
    }
});

window.addEventListener('resize', () => {
    if (gameRunning) {
        cancelAnimationFrame(animationFrame);
        createBalloons();
        animateParade();
    }
}); 