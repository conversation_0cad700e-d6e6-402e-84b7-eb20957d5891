/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

const choices = ['✊', '✋', '✌️'];
let players = [];
let rounds = [];
const tournamentElement = document.getElementById('tournament');
const countdownElement = document.getElementById('countdown');
const finalResultElement = document.getElementById('finalResult');
const messageElement = document.getElementById('message');
const startButton = document.getElementById('startButton');

function getRandomChoice() {
    return choices[Math.floor(Math.random() * choices.length)];
}

function determineWinner(choice1, choice2) {
    if (choice1 === choice2) return null;
    if (
        (choice1 === '✊' && choice2 === '✌️') ||
        (choice1 === '✋' && choice2 === '✊') ||
        (choice1 === '✌️' && choice2 === '✋')
    ) {
        return 0;
    }
    return 1;
}

function createMatch(player1, player2) {
    const matchElement = document.createElement('div');
    matchElement.className = 'match';

    const player1Element = createPlayerElement(player1);
    const player2Element = createPlayerElement(player2);

    matchElement.appendChild(player1Element);
    matchElement.appendChild(player2Element);

    const resultElement = document.createElement('div');
    resultElement.className = 'match-result';
    matchElement.appendChild(resultElement);

    if (player2 === 'Odd One Out') {
        matchElement.classList.add('bye-match');
        player2Element.classList.add('bye-player');
    }

    return matchElement;
}

function createPlayerElement(player) {
    const playerElement = document.createElement('div');
    playerElement.className = 'player';

    const nameElement = document.createElement('span');
    nameElement.className = 'player-name';
    nameElement.textContent = player;

    const choiceElement = document.createElement('span');
    choiceElement.className = 'choice';
    choiceElement.textContent = '?';

    playerElement.appendChild(nameElement);
    playerElement.appendChild(choiceElement);

    return playerElement;
}

async function playMatch(match, player1, player2) {
    if (player2 === 'Odd One Out') {
        match.querySelector('.match-result').textContent = `${player1} advances to the next round. (Odd number of players)`;
        match.querySelector('.match-result').classList.add('show');
        return player1;
    }

    let winner = null;
    while (winner === null) {
        winner = await playRound(match, player1, player2);
    }
    return winner;
}

function playRound(match, player1, player2) {
    return new Promise(resolve => {
        const choice1 = getRandomChoice();
        const choice2 = getRandomChoice();

        const player1Element = match.children[0];
        const player2Element = match.children[1];
        const resultElement = match.querySelector('.match-result');

        const choice1Element = player1Element.querySelector('.choice');
        const choice2Element = player2Element.querySelector('.choice');

        choice1Element.classList.add('bouncing');
        choice2Element.classList.add('bouncing');

        setTimeout(() => {
            choice1Element.textContent = choice1;
            choice2Element.textContent = choice2;

            choice1Element.classList.remove('bouncing');
            choice2Element.classList.remove('bouncing');

            const winner = determineWinner(choice1, choice2);

            if (winner === 0) {
                choice1Element.classList.add('winner');
                resultElement.textContent = `${player1} wins!`;
                resolve(player1);
            } else if (winner === 1) {
                choice2Element.classList.add('winner');
                resultElement.textContent = `${player2} wins!`;
                resolve(player2);
            } else {
                resultElement.textContent = "It's a tie! Playing again...";
                resolve(null);
            }

            resultElement.classList.add('show');

            setTimeout(() => {
                choice1Element.classList.remove('winner');
                choice2Element.classList.remove('winner');
            }, 1000);

        }, 1000);
    });
}

async function playFinalMatch(match, player1, player2) {
    match.classList.add('final-match');
    
    countdownElement.classList.add('show');
    for (let i = 3; i > 0; i--) {
        countdownElement.textContent = i;
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    countdownElement.textContent = "Fight!";
    await new Promise(resolve => setTimeout(resolve, 1000));
    countdownElement.classList.remove('show');

    const winner = await playMatch(match, player1, player2);

    finalResultElement.textContent = `${winner} is the tournament champion!`;
    finalResultElement.classList.add('show');

    window.parent.confetti?.({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
    });

    return winner;
}

async function playTournamentRound(roundPlayers) {
    const roundElement = document.createElement('div');
    roundElement.className = 'round';

    const winners = [];
    const matches = [];

    for (let i = 0; i < roundPlayers.length; i += 2) {
        const player1 = roundPlayers[i];
        const player2 = roundPlayers[i + 1] || 'Odd One Out';

        const match = createMatch(player1, player2);
        roundElement.appendChild(match);
        matches.push({ element: match, player1, player2 });
    }

    tournamentElement.appendChild(roundElement);

    for (const match of matches) {
        let winner;
        if (roundPlayers.length === 2) {
            // This is the final match
            winner = await playFinalMatch(match.element, match.player1, match.player2);
        } else {
            winner = await playMatch(match.element, match.player1, match.player2);
        }
        winners.push(winner);
        
        if (match.player2 !== 'Odd One Out') {
            setTimeout(() => {
                const resultElement = match.element.querySelector('.match-result');
                resultElement.style.opacity = '0.7';
            }, 2000);
        }
    }

    return winners;
}

function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
}

async function startTournament() {
    tournamentElement.innerHTML = '';
    countdownElement.textContent = '';
    countdownElement.classList.remove('show');
    finalResultElement.textContent = '';
    finalResultElement.classList.remove('show');
    messageElement.textContent = '';
    startButton.disabled = true;

    players = JSON.parse(localStorage.getItem('names') || '[]');
    if (players.length < 2) {
        messageElement.textContent = 'Please add at least 2 players before starting the tournament.';
        startButton.disabled = false;
        return;
    }

    shuffleArray(players);
    
    if (players.length % 2 !== 0) {
        const noticeElement = document.createElement('div');
        noticeElement.className = 'odd-player-notice';
        noticeElement.textContent = `There are ${players.length} players. One player will automatically advance in each round due to the odd number.`;
        tournamentElement.appendChild(noticeElement);
        players.push('Odd One Out');
    }

    let currentPlayers = [...players];

    while (currentPlayers.length > 1) {
        currentPlayers = await playTournamentRound(currentPlayers);
        if (currentPlayers.length > 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    startButton.disabled = false;
}

startButton.addEventListener('click', startTournament); 