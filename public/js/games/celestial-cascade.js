/*!
 * Person Picker (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const launchButton = document.getElementById('launchButton');
const resultDiv = document.getElementById('result');

let teamMembers = window.parent.names || [];
const celestialBodies = [];
const stars = [];
let gameActive = false;
let lastLanded = null;

function resizeCanvas() {
    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
    createStars();
}

class CelestialBody {
    constructor(name) {
        this.name = name;
        this.x = Math.random() * (canvas.width - 100) + 50; // Add 50px padding on each side
        this.y = -10;
        this.speed = Math.random() * 0.5 + 0.5;
        this.size = 5;
        this.color = `hsl(${Math.random() * 360}, 100%, 50%)`;
        this.landed = false;
        this.flames = [];
    }

    update() {
        if (!this.landed) {
            this.y += this.speed;
            if (this.y >= canvas.height - 5) {
                this.landed = true;
                this.y = canvas.height - 5;
                lastLanded = this;
            }
            this.updateFlames();
        }
    }

    updateFlames() {
        // Add new flames
        if (Math.random() < 0.3) {
            this.flames.push({
                x: this.x + (Math.random() - 0.5) * 10,
                y: this.y - this.size,
                size: Math.random() * 3 + 1,
                life: 1
            });
        }

        // Update existing flames
        for (let i = this.flames.length - 1; i >= 0; i--) {
            const flame = this.flames[i];
            flame.y -= 1;
            flame.life -= 0.02;
            if (flame.life <= 0) {
                this.flames.splice(i, 1);
            }
        }
    }

    draw() {
        // Draw flames
        this.flames.forEach(flame => {
            ctx.beginPath();
            ctx.arc(flame.x, flame.y, flame.size, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(255, ${Math.floor(Math.random() * 100) + 100}, 0, ${flame.life})`;
            ctx.fill();
            ctx.closePath();
        });

        // Draw celestial body
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fillStyle = this.color;
        ctx.fill();
        ctx.closePath();

        // Draw name
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Poppins';
        let textX = this.x + 7;
        let textWidth = ctx.measureText(this.name).width;
        
        // Adjust text position if it's too close to the right edge
        if (textX + textWidth > canvas.width - 5) {
            textX = this.x - textWidth - 7;
        }
        
        ctx.fillText(this.name, textX, this.y);
    }
}

function createStars() {
    stars.length = 0;
    for (let i = 0; i < 100; i++) {
        stars.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            size: Math.random() * 2
        });
    }
}

function drawStars() {
    ctx.fillStyle = '#ffffff';
    stars.forEach(star => {
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.closePath();
    });
}

function update() {
    if (gameActive) {
        celestialBodies.forEach(body => body.update());
        if (celestialBodies.every(body => body.landed)) {
            gameActive = false;
            const winner = lastLanded;
            resultDiv.textContent = `${winner.name} wins!`;
            resultDiv.classList.add('celebrate');
            window.parent.confetti?.({
                particleCount: 100,
                spread: 70,
                origin: { y: 0.6 }
            });
        }
    }
}

function draw() {
    ctx.fillStyle = '#000000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    drawStars();
    celestialBodies.forEach(body => body.draw());
}

function gameLoop() {
    update();
    draw();
    requestAnimationFrame(gameLoop);
}

function startGame() {
    if (!gameActive && teamMembers.length >= 2) {
        celestialBodies.length = 0;
        lastLanded = null;
        resultDiv.textContent = '';
        resultDiv.classList.remove('celebrate');
        teamMembers.forEach(name => celestialBodies.push(new CelestialBody(name)));
        gameActive = true;
    }
}

// Event listeners
window.addEventListener('resize', resizeCanvas);
launchButton.addEventListener('click', startGame);

// Initialize
resizeCanvas();
gameLoop(); 