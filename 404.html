<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - Person Picker</title>
    <link rel="stylesheet" href="/styles.css">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <style>
        .error-container {
            text-align: center;
            padding: 2rem;
            max-width: 600px;
            margin: 0 auto;
        }
        .error-code {
            font-size: 6rem;
            color: #ff6b6b;
            margin: 0;
        }
        .error-message {
            font-size: 1.5rem;
            margin: 1rem 0;
        }
        .suggested-links {
            margin: 2rem 0;
        }
        .suggested-links a {
            display: inline-block;
            margin: 0.5rem;
            padding: 0.5rem 1rem;
            background-color: #4a90e2;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .suggested-links a:hover {
            background-color: #357abd;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <h1 class="error-code">404</h1>
        <p class="error-message">Oops! The page you're looking for seems to have wandered off.</p>
        <div class="suggested-links">
            <h2>Try these fun alternatives:</h2>
            <a href="/">Home Page</a>
            <a href="/wheel-of-fortune.html">Wheel of Fortune</a>
            <a href="/rock-paper-scissors-tournament.html">Rock Paper Scissors</a>
            <a href="/grid-quest.html">Grid Quest</a>
        </div>
    </div>
</body>
</html> 