name: Deploy to host
on:
  workflow_dispatch:  # Allows manual triggering
  push:
    branches:
      - main
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Install SSH Key
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.SERVER_SSH_KEY }}
        known_hosts: ${{ secrets.KNOWN_HOSTS }}

    - name: Deploy to host
      env:
        SERVER_IP: ${{ secrets.SERVER_IP }}
        SERVER_USER: ${{ secrets.SERVER_USER }}
        DEPLOY_DIR: ${{ secrets.DEPLOY_DIR }}
      run: |
        rsync -avz --delete --exclude='.git/' --exclude='.gitignore' --exclude='.github/' ./ $SERVER_USER@$SERVER_IP:$DEPLOY_DIR