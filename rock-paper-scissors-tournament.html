<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rock Paper Scissors Tournament</title>
    <link rel="stylesheet" href="/public/css/games/rock-paper-scissors-tournament.css">
    <script src="/public/js/confetti.js"></script>
</head>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-CXC0589Q5T"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-CXC0589Q5T');
</script>
<body>
    <div class="container">
        <h1>Rock Paper Scissors Tournament</h1>
        <div id="description">
            Everyone battles it out in a tournament-style Rock Paper Scissors game. Who will emerge victorious?
        </div>
        <div id="tournament"></div>
        <div id="countdown" class="countdown"></div>
        <div id="finalResult" class="final-result"></div>
        <div id="message"></div>
        <button id="startButton">Start Tournament</button>
    </div>
    <script src="/public/js/games/rock-paper-scissors-tournament.js"></script>
</body>
</html>